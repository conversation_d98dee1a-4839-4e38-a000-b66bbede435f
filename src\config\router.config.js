import router from '@/router';
import axios from 'axios';
import store from '../store';
import { getRegust } from '@/util/module/common';
import { Message } from 'view-design';
import { getAsyncRoutes } from '@/router/asyncRouter';
import thirdPartyConfig from '@/config/thirdParty.config';

// 路由钩子，做一些跳转的配置
router.beforeEach(async (to, from, next) => {
  try {
    // 是否有跳转权限
    let flag = await hasSkipPermission(to);
    if (!flag) {
      Message.warning('未分配该页面权限，请联系管理员分配！');
      return;
    }

    // 当切换路由时阻止正在访问的接口全部取消访问
    const CancelToken = axios.CancelToken;
    store.state.common.source.cancel && store.state.common.source.cancel('取求');
    store.dispatch('common/setSource', CancelToken.source());
    if (to.name == 'error_401') {
      next();
      return;
    }

    // 判断是否授权
    const author = !!window.sessionStorage.getItem('authorInfo');
    if (author && author !== 'undefined') {
      store.commit(
        'common/setAuthorInfo',
        window.sessionStorage.getItem('authorInfo') ? JSON.parse(window.sessionStorage.getItem('authorInfo')) : '',
      );
      init(to, from, next);
    } else {
      store
        .dispatch('common/setAuthor')
        .then(() => {
          init(to, from, next);
        })
        .catch((url) => {
          next();
          window.sessionStorage.setItem('authorUrl', url);
          window.location.href = `${url}?redirect=${encodeURIComponent(window.location.href)}`;
          return;
        });
    }
    // init(to, from, next)
    removeTooltipNode();
  } catch (err) {
    console.log(err);
  }
});

async function init(to, from, next) {
  try {
    // 获取系统配置信息
    !Object.keys(store.state.common.systemConfig).length && (await store.dispatch('common/setSystemConfig'));
    // 若满足第三方参数校验走第三方登录
    if (await needThirdParty(to)) {
      await thirdPartLogin(to, next);
      return;
    }
    console.log("=======initRouter====",to.name)
    if (to.name !== 'login') {
      const hasToken = !!window.sessionStorage.getItem('token');
      const route = store.state.permission.routerList;
      const hasRouters = route && route.length > 0;
      if (hasToken && hasRouters) {
        /* 注意：cacheRouterList 会存在 相同的path，但会根据 路由参数中的to.query.componentName渲染不同的组件，如果仅仅判断 row.path === to.path，
      find找到的route不一定是正确的，就会导致 !to.query.componentName 为true时，路由参数被清空了，
      比如  检测管理页面 和 该页面进去的指标详情页面（xxx-指标详情）  路由path是一样的，如下操作：
      当两个页面都已打开，并缓存到cacheRouterList后，这个时候关闭 检测管理页面，然后重新打开检测管理页面，就会出现bug，导致 xxx-指标详情 的路由参数被改动了，页面渲染不正确 */
        const route = store.state.tabs.cacheRouterList.find(
          (row) =>
            row.path === to.path &&
            ((!to.query?.componentName && !row.meta?.queryParams?.componentName) ||
              (to.query?.componentName && to.query?.componentName === row.meta?.queryParams?.componentName)),
        );
        let cacheRouterList = store.state.tabs.cacheRouterList;
        if (!route) {
          //router不存在时存储该router和query
          const newRoute = store.state.permission.routerList.find((row) => row.path === to.path);
          const allRouterList = store.state.permission.allRouterList;
          const systemRoute = allRouterList.find((row) => {
            return `/${row.permission}` === to.path || row.permission === to.name;
          });
          /**
           * 1. 判断该菜单在系统管理中是否有权限，有权限就进入
           * 2. 没有权限判断是否是系统管理中创建的菜单，如果是则提示没有此菜单的权限
           * 3. 如果不是系统管理中的菜单则直接进入
           */
          if (newRoute) {
            cacheRouterList.push({
              ...newRoute,
              meta: {
                routeName: to.name,
                queryParams: to.query,
              },
            });
          } else if (systemRoute) {
            Message.warning(`您没有【${systemRoute.resourceCname}】的权限，请联系管理员添加`);
            return;
          } else {
            next();
          }
        } else {
          //router 存在时更新query 但不包括参数中有componentName的， 否则会导致create-tabs 失效
          if (!to.query.componentName) {
            route.meta = {
              routeName: to.name,
              queryParams: to.query,
            };
          }
        }
        store.dispatch('tabs/setCacheRouterList', cacheRouterList);
        next();
      }
      if (hasToken && !hasRouters) _getRoutes(to, next);
      if (!hasToken) _singleLogin(to, from, next);
    } else {
      next();
    }
  } catch (err) {
    console.log(err);
  }
}

// 可以根据跳转到的页面，显示不同颜色的loadingBar
router.afterEach(() => {});

async function _getRoutes(to, next,isClearQuery = false) {
  try {
    await store.dispatch('permission/setRouterList');
    const accessRoutes = getAsyncRoutes(store.state.permission.routerList);
    console.log("====accessRoutes====",accessRoutes)
    if (accessRoutes.length > 0) {
      // 动态添加格式化过的路由
      accessRoutes.forEach((route) => {
        router.addRoute(route);
      });
      const ext = isClearQuery ? {query : {}} : {};
      next({ ...to,...ext, replace: true });
    } else {
      Message.warning('该用户未分配菜单权限,请联系管理员分配！');
      next({
        name: 'login',
        query: {
          redirect: router.currentRoute.name,
        }, //登录成功后跳入浏览的当前页面
      });
    }
  } catch (err) {
    console.log(err, 'err');
    next({
      name: 'login',
      query: {
        redirect: router.currentRoute.name,
      }, //登录成功后跳入浏览的当前页面
    });
  }
}

function _singleLogin(to, from, next) {
  let params = getRegust();
  // 这里做单点登录 从彩云会免登录到vid系统中，账号密码从href中直接获取
  if (params.password) {
    // 重新返回登录界面登录
    next({
      name: 'login',
      query: {
        redirect: router.currentRoute.name,
        username: params.username,
        password: params.password,
      }, //登录成功后跳入浏览的当前页面
    });
  } else {
    // 重新返回登录界面登录
    next({
      name: 'login',
      query: {
        redirect: router.currentRoute.name,
      }, //登录成功后跳入浏览的当前页面
    });
  }
}

async function hasSkipPermission(to, from) {
  let flag = true;
  try {
    // 是否有权限跳转到 【视图治理】
    if (to.name === 'governanceautomatic') {
      flag = await store.dispatch('governanceconfig/hasSkipPermission', { to, from });
    }
    return flag;
  } catch (error) {
    return flag;
  }
}

/**
 * http://***************:65533/zentao/bug-view-4199.html
 */
function removeTooltipNode() {
  let tooltipNode = document.querySelectorAll('.ivu-tooltip-popper, .tips-box');
  tooltipNode.forEach((node) => {
    node.style.display = 'none';
  });
}

//是否走单点登录，需同时满足thirdPartyConfig的key及参数的校验
async function needThirdParty(to) {
  try {
    if (to.query?.useDesignatedUser === '1') {
      return true;
    }
    let {distinguishVersion} = store.state.common.systemConfig;
    const hasDistinguishVersion = Object.keys(thirdPartyConfig).includes(distinguishVersion);
    //若使用pki登录不校验参数
    if (hasDistinguishVersion && thirdPartyConfig[distinguishVersion].isUsePki) {
      return false;
    }
    const validate = await thirdPartyConfig[distinguishVersion]?.validateParams(to.query,to);
    return hasDistinguishVersion && validate;
  } catch (err) {
    console.log(err);
    return false;
  }
}

//获取系统的登录token
async function thirdPartLogin(to, next) {
  try {
    let { distinguishVersion } = store.state.common.systemConfig;
    store.commit('user/setThirdPartyLoading', true);
    if(to.query?.useDesignatedUser === '1'){
      distinguishVersion = 'useDesignatedUser';
    }
    await thirdPartyConfig[distinguishVersion].loginAsync(to.query);
  } catch (err) {
    console.error(err, 'err');
  } finally {
    store.commit('user/setThirdPartyLoading', false);
    //清空参数以避免死循环
    console.log("thirdPartLogin")
    await _getRoutes(to, next,true);
    // if (to.name === 'login') {
    //   next({ ...to, name: '/', query: {} });
    // } else {
    //   next({ ...to, query: {} });
    // }
  }
}
export default router;
